import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Save, RotateCcw, BarChart3, TrendingUp, User, FileText, DollarSign, Calendar, AlertTriangle } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Notification from '../common/Notification';
import { useDashboard } from '../../contexts/DashboardContext';

const FaceAmountPage = () => {
  // Add dashboard context for policy info
  const { selectedCustomerData, selectedPolicyData, setActiveTab, addScenario, scenarios, selectedScenarios } = useDashboard();

  // Mock customer data - in real app this would come from props or context
  const [customerData] = useState({
    name: '<PERSON>',
    customer_id: 'CUST001',
    policy_number: 'POL123456',
    coverage: '$500,000',
    premium: '$2,500/year'
  });

  const [selectedPolicy] = useState({
    name: 'Universal Life',
    coverage: '$500,000',
    premium: '$2,500/year'
  });

  const [faceAmountData, setFaceAmountData] = useState({
    current_death_benefit: 500000,
    current_death_benefit_option: 'A', // A = Level, B = Increasing
    want_to_change: false,
    change_immediately: false,
    change_amount: 0,
    // Death Benefit Option changes
    change_option_a_to_b: false,
    change_option_b_to_a: false,
    option_change_now: false,
    option_change_age: 0
  });

  // State for the new modify face amount by year functionality
  const [modifyByYearData, setModifyByYearData] = useState({
    selectedTypes: {
      age: false,
      policyYear: false,
      calendarYear: false
    },
    ageRange: {
      start: 40, // Will be updated with actual current age
      end: 100
    },
    policyYearRange: {
      start: 1, // Will be updated with actual current policy year
      end: 100
    },
    calendarYearRange: {
      start: 2024, // Will be updated with actual current year
      end: 2100
    },
    isEditing: false,
    tableData: [] as TableRowData[]
  });

  type FaceAmountScenario = {
    id: number;
    timestamp: string;
    customer_name: string;
    policy_number: string;
    data: typeof faceAmountData;
    summary: string[];
  };

  type TableRowData = {
    age: number;
    policyYear: string;
    calendarYear: number;
    faceAmount: number;
  };
  const [faceAmountHistory, setFaceAmountHistory] = useState<FaceAmountScenario[]>([]);
  const [showIllustration, setShowIllustration] = useState(false);
  const [showComparison, setShowComparison] = useState(false);
  const [notification, setNotification] = useState<{ message: string; type?: 'success' | 'error' } | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Show notification temporarily
  const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
    setNotification({ message, type });
  };

  // Calculate current age from DOB
  const calculateCurrentAge = (): number => {
    if (!selectedCustomerData?.details?.DOB) return 40; // Default age

    const dob = selectedCustomerData.details.DOB;
    console.log('DOB from data:', dob); // Debug log

    // Handle different date formats
    let birthDate: Date;

    if (dob.includes('.')) {
      // Format: DD.MM.YYYY
      const dobParts = dob.split('.');
      if (dobParts.length !== 3) return 40;
      const [day, month, year] = dobParts.map(Number);
      birthDate = new Date(year, month - 1, day);
    } else if (dob.includes('/')) {
      // Format: MM/DD/YYYY or DD/MM/YYYY
      const dobParts = dob.split('/');
      if (dobParts.length !== 3) return 40;
      const [first, second, year] = dobParts.map(Number);
      // Assume MM/DD/YYYY format
      birthDate = new Date(year, first - 1, second);
    } else if (dob.includes('-')) {
      // Format: YYYY-MM-DD
      birthDate = new Date(dob);
    } else {
      return 40; // Default if format not recognized
    }

    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    console.log('Calculated age:', age); // Debug log
    return Math.max(0, age); // Ensure age is not negative
  };

  // Calculate current policy year from issue date
  const calculateCurrentPolicyYear = (): number => {
    const issueDate = (selectedPolicyData as any)?.issueDate || (selectedPolicyData as any)?.policyStartDate;
    if (issueDate) {
      const issue = new Date(issueDate);
      const today = new Date();
      const yearsDiff = today.getFullYear() - issue.getFullYear();
      const monthsDiff = today.getMonth() - issue.getMonth();
      const daysDiff = today.getDate() - issue.getDate();

      // Calculate total months more accurately
      let totalMonths = yearsDiff * 12 + monthsDiff;
      if (daysDiff >= 0) {
        totalMonths += 1; // Add current month if we've passed the issue day
      }

      const policyYear = Math.max(1, Math.ceil(totalMonths / 12));
      return policyYear;
    }
    return 1; // Default if no issue date
  };

  // Get current calendar year
  const getCurrentYear = (): number => {
    return new Date().getFullYear();
  };

  // Initialize ranges with actual values
  React.useEffect(() => {
    const currentAge = calculateCurrentAge();
    const currentPolicyYear = calculateCurrentPolicyYear();
    const currentYear = getCurrentYear();

    setModifyByYearData(prev => ({
      ...prev,
      ageRange: {
        start: currentAge,
        end: 100
      },
      policyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      calendarYearRange: {
        start: currentYear,
        end: 2100
      }
    }));
  }, [selectedCustomerData, selectedPolicyData]);

  // Generate table data based on selected types and ranges
  const generateTableData = (): TableRowData[] => {
    const { selectedTypes, ageRange, policyYearRange, calendarYearRange } = modifyByYearData;

    // Determine which range to use based on selected types
    let startYear = 0;
    let endYear = 0;

    if (selectedTypes.age) {
      const currentAge = calculateCurrentAge();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (ageRange.start - currentAge);
      endYear = currentCalendarYear + (ageRange.end - currentAge);
    } else if (selectedTypes.policyYear) {
      const currentPolicyYear = calculateCurrentPolicyYear();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (policyYearRange.start - currentPolicyYear);
      endYear = currentCalendarYear + (policyYearRange.end - currentPolicyYear);
    } else if (selectedTypes.calendarYear) {
      startYear = calendarYearRange.start;
      endYear = calendarYearRange.end;
    }

    if (startYear === 0 || endYear === 0 || startYear > endYear) return [];

    const currentAge = calculateCurrentAge();
    const currentCalendarYear = getCurrentYear();
    const currentPolicyYear = calculateCurrentPolicyYear();

    const data: TableRowData[] = [];

    // Limit to maximum 12 entries
    const maxEntries = 12;
    const totalYears = endYear - startYear + 1;
    const actualEndYear = totalYears > maxEntries ? startYear + maxEntries - 1 : endYear;

    for (let year = startYear; year <= actualEndYear; year++) {
      const row: TableRowData = {
        age: currentAge + (year - currentCalendarYear),
        policyYear: `Year ${currentPolicyYear + (year - currentCalendarYear)}`,
        calendarYear: year,
        faceAmount: faceAmountData.current_death_benefit // Default face amount
      };

      data.push(row);
    }

    return data;
  };

  // Update table data when selections change
  React.useEffect(() => {
    const newTableData = generateTableData();
    setModifyByYearData(prev => ({ ...prev, tableData: newTableData }));
  }, [modifyByYearData.selectedTypes, modifyByYearData.ageRange, modifyByYearData.policyYearRange, modifyByYearData.calendarYearRange, faceAmountData.current_death_benefit]);

  // Update form data
  const updateFormData = (field: keyof typeof faceAmountData, value: any) => {
    setFaceAmountData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Save scenario
  const saveScenario = async () => {
    setIsSaving(true);
    try {
      const now = new Date();
      const scenarioEntry = {
        id: (Date.now().toString() + Math.random()),
        name: `Face Amount Scenario - ${(selectedCustomerData?.name || customerData.name)}`,
        policyId: selectedCustomerData?.policyNumber || customerData.policy_number,
        asIsDetails: `Current Death Benefit: $${faceAmountData.current_death_benefit.toLocaleString()}`,
        whatIfOptions: generateScenarioSummary(),
        category: 'face-amount' as const,
        keyPoints: generateScenarioSummary(),
        impact: 'neutral',
        data: { ...faceAmountData },
        createdAt: now,
        updatedAt: now,
      };
      await addScenario(scenarioEntry);
      showNotification('AS-IS configuration saved successfully and added to Selected Scenarios!');
    } catch (error) {
      showNotification('Error saving scenario!', 'error');
      console.error('Error saving Face Amount scenario:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Generate scenario summary
  const generateScenarioSummary = () => {
    const summary = [];

    // Current face amount
    summary.push(`Current Face Amount Death Benefit: $${faceAmountData.current_death_benefit.toLocaleString()}`);
    summary.push(`Current Death Benefit Option: ${faceAmountData.current_death_benefit_option} (${faceAmountData.current_death_benefit_option === 'A' ? 'Level' : 'Increasing'})`);

    // Death benefit option changes
    if (faceAmountData.change_option_a_to_b) {
      const timing = faceAmountData.option_change_now ? 'now' : `starting from age ${faceAmountData.option_change_age}`;
      summary.push(`Change from Option A (Level) to Option B (Increasing) ${timing}`);
    }
    if (faceAmountData.change_option_b_to_a) {
      const timing = faceAmountData.option_change_now ? 'now' : `starting from age ${faceAmountData.option_change_age}`;
      summary.push(`Change from Option B (Increasing) to Option A (Level) ${timing}`);
    }

    // Face amount changes
    if (faceAmountData.change_immediately) {
      summary.push(`Change face amount to $${faceAmountData.change_amount.toLocaleString()} now`);
    }
    if (faceAmountData.want_to_change) {
      summary.push("Modify face amount by year schedule selected");
    }

    return summary.length > 2 ? summary : ["Current face amount configuration - no changes selected"];
  };

  // Reset form
  const resetForm = () => {
    setFaceAmountData({
      current_death_benefit: 500000,
      current_death_benefit_option: 'A',
      want_to_change: false,
      change_immediately: false,
      change_amount: 0,
      change_option_a_to_b: false,
      change_option_b_to_a: false,
      option_change_now: false,
      option_change_age: 0
    });
    showNotification('Form reset!');
  };

  // Generate illustration data
  const generateIllustrationData = () => {
    const years = Array.from({ length: 20 }, (_, i) => 2024 + i);
    
    return years.map(year => {
      const yearsFromNow = year - 2024;
      const currentValue = faceAmountData.current_death_benefit * Math.pow(1.02, yearsFromNow);
      const newValue = faceAmountData.want_to_change && faceAmountData.change_immediately
        ? faceAmountData.change_amount * Math.pow(1.02, yearsFromNow)
        : currentValue;
      
      return {
        year,
        currentBenefit: Math.round(currentValue),
        newBenefit: Math.round(newValue)
      };
    });
  };

  // Generate illustration
  const generateIllustration = () => {
    setShowIllustration(true);
    showNotification('Face Amount illustration generated successfully!');
  };

  // Load scenario
  const loadScenario = (scenario: any) => {
    setFaceAmountData({ ...scenario.data });
    showNotification(`Scenario ${scenario.id} loaded!`);
  };



  // Filter selected scenarios for this category
  const selectedFaceAmountScenarios = scenarios.filter(
    s => selectedScenarios.includes(s.id) && s.category === 'face-amount'
  );

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="space-y-6">
        {/* <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">FACE AMOUNT ILLUSTRATION</h1>
          <p className="text-gray-600 dark:text-gray-400">Configure the face amount and death benefit options for the selected policy.</p>
        </div> */}

        {/* Notification */}
        {notification && (
          <Notification
            message={notification.message}
            type={notification.type}
            onClose={() => setNotification(null)}
          />
        )}

        {/* Show message if no policy is selected */}
        {(!selectedCustomerData || !selectedPolicyData) ? (
          <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
                <p className="text-yellow-700 dark:text-yellow-300">
                  Please go to the Policy Selection tab first to search and select a customer policy before configuring the Face Amount illustration.
                </p>
                <Button
                  onClick={() => setActiveTab('policy-selection')}
                  variant="outline"
                  className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
                >
                  Go to Policy Selection
                </Button>
              </div>
            </div>
          </Card>
        ) : (
          <>
            {/* Section 1: Current Face Amount Death Benefit */}
          <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-blue-500">
            <h3 className="text-2xl font-bold text-black mb-6 pb-4 border-b-2 border-gray-200">
              Scenarios
            </h3>

            {/* Scenarios Description */}
            <div className="bg-blue-50 p-6 rounded-lg border border-blue-200 mb-6">
              <p className="text-lg text-gray-800 leading-relaxed">
                You may want to increase or decrease your policy's face amount based on your current protection needs. Changing the death benefit will impact premiums, cash value growth, and overall policy performance. Scenarios will be based on the Current Interest Rate.
              </p>
            </div>

            {/* Current Face Amount Death Benefit Display */}
            <div className="bg-green-50 p-6 rounded-lg border border-green-200 mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-xl font-bold text-black mb-2">Current Face Amount Death Benefit</h4>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-green-700">
                    ${(selectedPolicyData?.coverage?.replace(/[^0-9]/g, '') || faceAmountData.current_death_benefit.toString()).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  </div>
                  <p className="text-sm text-gray-500">Death Benefit</p>
                </div>
              </div>
            </div>

            {/* Death Benefit Options section removed */}

            <div className="bg-gray-50 p-6 rounded-lg border">
              <div className="space-y-4">
                <div>
                  <label className="flex items-center text-black font-semibold mb-2">
                    <input
                      type="checkbox"
                      checked={faceAmountData.change_immediately}
                      onChange={(e) => updateFormData('change_immediately', e.target.checked)}
                      className="mr-2"
                    />
                    Change to New Face Amount.
                  </label>
                  {faceAmountData.change_immediately && (
                    <div className="grid grid-cols-2 gap-4 mt-2">
                      <div>
                        <label className="block text-sm font-bold text-black mb-1">
                          New Face Amount:
                        </label>
                        <input
                          type="number"
                          value={faceAmountData.change_amount}
                          onChange={(e) => updateFormData('change_amount', parseInt(e.target.value) || 0)}
                          className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                          step="10000"
                          placeholder="Enter new face amount"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-6">
                <div>
                  <label className="flex items-center text-black font-semibold mb-2">
                    <input
                      type="checkbox"
                      checked={faceAmountData.want_to_change}
                      onChange={(e) => updateFormData('want_to_change', e.target.checked)}
                      className="mr-2"
                    />
                    Modify face amount by year
                  </label>
                  {faceAmountData.want_to_change && (
                    <div className="mt-4 space-y-6">
                      {/* Type Selection Checkboxes */}
                      <div className="grid grid-cols-3 gap-4">
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={modifyByYearData.selectedTypes.age}
                            onChange={(e) => setModifyByYearData(prev => ({
                              ...prev,
                              selectedTypes: { ...prev.selectedTypes, age: e.target.checked }
                            }))}
                            className="mr-2"
                          />
                          Age
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={modifyByYearData.selectedTypes.policyYear}
                            onChange={(e) => setModifyByYearData(prev => ({
                              ...prev,
                              selectedTypes: { ...prev.selectedTypes, policyYear: e.target.checked }
                            }))}
                            className="mr-2"
                          />
                          Policy Year
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={modifyByYearData.selectedTypes.calendarYear}
                            onChange={(e) => setModifyByYearData(prev => ({
                              ...prev,
                              selectedTypes: { ...prev.selectedTypes, calendarYear: e.target.checked }
                            }))}
                            className="mr-2"
                          />
                          Calendar Year
                        </label>
                      </div>

                      {/* Age Range Toggle Bars */}
                      {modifyByYearData.selectedTypes.age && (
                        <div className="space-y-4">
                          {/* <h4 className="text-lg font-semibold text-black">Age Range</h4> */}
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, start: Math.max(calculateCurrentAge(), prev.ageRange.start - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {modifyByYearData.ageRange.start}
                                </span>
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, start: Math.min(100, prev.ageRange.start + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Age</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, end: Math.max(prev.ageRange.start, prev.ageRange.end - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {modifyByYearData.ageRange.end}
                                </span>
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, end: Math.min(100, prev.ageRange.end + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Policy Year Range Toggle Bars */}
                      {modifyByYearData.selectedTypes.policyYear && (
                        <div className="space-y-4">
                          {/* <h4 className="text-lg font-semibold text-black">Policy Year Range</h4> */}
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, start: Math.max(calculateCurrentPolicyYear(), prev.policyYearRange.start - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {modifyByYearData.policyYearRange.start}
                                </span>
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, start: Math.min(100, prev.policyYearRange.start + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Policy Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, end: Math.max(prev.policyYearRange.start, prev.policyYearRange.end - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {modifyByYearData.policyYearRange.end}
                                </span>
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, end: Math.min(100, prev.policyYearRange.end + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Calendar Year Range Toggle Bars */}
                      {modifyByYearData.selectedTypes.calendarYear && (
                        <div className="space-y-4">
                          {/* <h4 className="text-lg font-semibold text-black">Calendar Year Range</h4> */}
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, start: Math.max(getCurrentYear(), prev.calendarYearRange.start - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {modifyByYearData.calendarYearRange.start}
                                </span>
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, start: Math.min(2100, prev.calendarYearRange.start + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Calendar Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, end: Math.max(prev.calendarYearRange.start, prev.calendarYearRange.end - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {modifyByYearData.calendarYearRange.end}
                                </span>
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, end: Math.min(2100, prev.calendarYearRange.end + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Modify Schedule Button */}
                      <div className="flex justify-start">
                        <button
                          onClick={() => setModifyByYearData(prev => ({ ...prev, isEditing: !prev.isEditing }))}
                          className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors"
                        >
                          {modifyByYearData.isEditing ? 'Lock Schedule' : 'Modify Schedule'}
                        </button>
                      </div>

                      {/* Data Table */}
                      <div className="mt-4">
                        <div className="overflow-x-auto">
                          <table className="w-full border-collapse border border-gray-300">
                            <thead>
                              <tr className="bg-gray-100">
                                <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Age</th>
                                <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Policy Year</th>
                                <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Calendar Year</th>
                                <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Face Amount</th>
                              </tr>
                            </thead>
                            <tbody>
                              {modifyByYearData.tableData.length === 0 ? (
                                <tr>
                                  <td
                                    colSpan={4}
                                    className="border border-gray-300 px-4 py-2 text-center text-gray-500"
                                  >
                                    Select year range to populate table
                                  </td>
                                </tr>
                              ) : (
                                modifyByYearData.tableData.map((row, index) => (
                                  <tr key={index}>
                                    <td className="border border-gray-300 px-4 py-2">{row.age}</td>
                                    <td className="border border-gray-300 px-4 py-2">{row.policyYear}</td>
                                    <td className="border border-gray-300 px-4 py-2">{row.calendarYear}</td>
                                    <td className="border border-gray-300 px-4 py-2">
                                      <input
                                        type="number"
                                        value={row.faceAmount}
                                        readOnly={!modifyByYearData.isEditing}
                                        onChange={(e) => {
                                          if (modifyByYearData.isEditing) {
                                            const newTableData = [...modifyByYearData.tableData];
                                            newTableData[index].faceAmount = parseInt(e.target.value) || 0;
                                            setModifyByYearData(prev => ({ ...prev, tableData: newTableData }));
                                          }
                                        }}
                                        className={`w-full p-2 border rounded ${
                                          modifyByYearData.isEditing
                                            ? 'border-blue-300 bg-white'
                                            : 'border-gray-300 bg-gray-100'
                                        }`}
                                        step="10000"
                                      />
                                    </td>
                                  </tr>
                                ))
                              )}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Section 2: Death Benefit Option A to B */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">2. Your current Death Benefit Option is A (Level). Do you want to switch to option B (level to increasing)?</h3>

            <div className="space-y-6">
              <div className="mb-4">
                <div className="space-y-4">
                  {/* Option: Change to Option B now */}
                  <div className="pl-4">
                    <label className="flex items-center space-x-3 text-lg font-semibold text-black">
                      <input
                        type="radio"
                        name="optionAtoB"
                        checked={faceAmountData.change_option_a_to_b && faceAmountData.option_change_now}
                        onChange={() => {
                          updateFormData('change_option_a_to_b', true);
                          updateFormData('option_change_now', true);
                        }}
                        className="w-5 h-5 text-black"
                      />
                      <span>Change to Option B now</span>
                    </label>
                  </div>

                  {/* Option: Change starting from age */}
                  <div className="pl-4">
                    <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                      <input
                        type="radio"
                        name="optionAtoB"
                        checked={faceAmountData.change_option_a_to_b && !faceAmountData.option_change_now}
                        onChange={() => {
                          updateFormData('change_option_a_to_b', true);
                          updateFormData('option_change_now', false);
                        }}
                        className="w-5 h-5 text-black mt-1"
                      />
                      <div className="flex-1">
                        <span>starting from age</span>
                        <div className="mt-2">
                          <input
                            type="number"
                            value={faceAmountData.option_change_age}
                            onChange={(e) => updateFormData('option_change_age', parseInt(e.target.value) || 0)}
                            className="w-24 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                            placeholder="Age"
                            min="1"
                            max="100"
                            disabled={!faceAmountData.change_option_a_to_b || faceAmountData.option_change_now}
                          />
                        </div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Section 3: Death Benefit Option B to A */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">3. Your current Death Benefit Option is B (Increasing). Switch to option A (Increasing to Level)?</h3>

            <div className="space-y-6">
              <div className="mb-4">
                <div className="space-y-4">
                  {/* Option: Change to Option A now */}
                  <div className="pl-4">
                    <label className="flex items-center space-x-3 text-lg font-semibold text-black">
                      <input
                        type="radio"
                        name="optionBtoA"
                        checked={faceAmountData.change_option_b_to_a && faceAmountData.option_change_now}
                        onChange={() => {
                          updateFormData('change_option_b_to_a', true);
                          updateFormData('option_change_now', true);
                        }}
                        className="w-5 h-5 text-black"
                      />
                      <span>Change to Option A now</span>
                    </label>
                  </div>

                  {/* Option: Change starting from age */}
                  <div className="pl-4">
                    <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                      <input
                        type="radio"
                        name="optionBtoA"
                        checked={faceAmountData.change_option_b_to_a && !faceAmountData.option_change_now}
                        onChange={() => {
                          updateFormData('change_option_b_to_a', true);
                          updateFormData('option_change_now', false);
                        }}
                        className="w-5 h-5 text-black mt-1"
                      />
                      <div className="flex-1">
                        <span>starting from age</span>
                        <div className="mt-2">
                          <input
                            type="number"
                            value={faceAmountData.option_change_age}
                            onChange={(e) => updateFormData('option_change_age', parseInt(e.target.value) || 0)}
                            className="w-24 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                            placeholder="Age"
                            min="1"
                            max="100"
                            disabled={!faceAmountData.change_option_b_to_a || faceAmountData.option_change_now}
                          />
                        </div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center mb-8">
            <Button
              onClick={saveScenario}
              variant="primary"
              loading={isSaving}
              disabled={isSaving}
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save Face Amount Illustration</span>
            </Button>
            <Button
              onClick={() => { setFaceAmountHistory([]); showNotification('All face amount scenarios reset!'); }}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
          </div>

          {/* Illustration Results */}
          {showIllustration && (
            <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-green-500">
              <h3 className="text-2xl font-bold text-black mb-6 flex items-center gap-2">
                <BarChart3 className="w-6 h-6" />
                Face Amount Illustration Results
              </h3>
              
              <div className="mb-6">
                <h4 className="font-bold text-black mb-2">Current Scenario:</h4>
                <p>• Current Face Amount Death Benefit: ${faceAmountData.current_death_benefit.toLocaleString()}</p>

                {(faceAmountData.change_immediately || faceAmountData.want_to_change) && (
                  <div className="mt-4">
                    <h4 className="font-bold text-black mb-2">Face Amount Changes:</h4>
                    {faceAmountData.change_immediately && (
                      <p>• Change to ${faceAmountData.change_amount.toLocaleString()} now</p>
                    )}
                    {faceAmountData.want_to_change && (
                      <p>• Modify face amount by year schedule</p>
                    )}
                  </div>
                )}
              </div>

              <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={generateIllustrationData()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="year" />
                    <YAxis tickFormatter={(value: number | string) => `$${(Number(value) / 1000).toFixed(0)}k`} />
                    <Tooltip formatter={(value: number | string) => [`$${Number(value).toLocaleString()}`, '']} />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="currentBenefit" 
                      stroke="#2563eb" 
                      strokeWidth={3}
                      name="Current Death Benefit"
                    />
                    {faceAmountData.want_to_change && faceAmountData.change_immediately && (
                      <Line 
                        type="monotone" 
                        dataKey="newBenefit" 
                        stroke="#dc2626" 
                        strokeWidth={3}
                        strokeDasharray="10 5"
                        name="New Death Benefit"
                      />
                    )}
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          )}

          {/* Scenario Comparison */}
          {showComparison && (
            <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-purple-500">
              <h3 className="text-2xl font-bold text-black mb-6 flex items-center gap-2">
                <TrendingUp className="w-6 h-6" />
                Face Amount Scenario Comparison
              </h3>
              
              {faceAmountHistory.length > 0 ? (
                <div className="space-y-4">
                  <h4 className="font-bold text-black">Saved Scenarios:</h4>
                  {faceAmountHistory.map((scenario) => (
                    <div key={scenario.id} className="bg-gray-50 p-4 rounded-lg border">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h5 className="font-bold text-black">Scenario {scenario.id} - {scenario.timestamp}</h5>
                          <p className="text-sm text-gray-600">Customer: {scenario.customer_name}</p>
                          <p className="text-sm text-gray-600">Policy: {scenario.policy_number}</p>
                        </div>
                        <button
                          onClick={() => loadScenario(scenario)}
                          className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors text-sm"
                        >
                          Load Scenario
                        </button>
                      </div>
                      <div>
                        <p className="font-semibold text-black mb-1">Summary:</p>
                        <ul className="text-sm text-gray-700">
                          {scenario.summary.map((item, index) => (
                            <li key={index}>• {item}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
                  <p className="text-gray-600">No saved scenarios available. Save a scenario first to enable comparison.</p>
                </div>
              )}
            </div>
          )}

          {/* Selected Scenarios Section */}
          </>
        )}
      </div>
    </div>
  );
};

export default FaceAmountPage;